// app/Middleware/TenantResolver.ts
import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import Database from '@ioc:Adonis/Lucid/Database'
import Tenant from 'App/Models/Tenant'

export default class TenantResolver {
  public async handle({ request }: HttpContextContract, next: () => Promise<void>) {
    // 👇 You can choose to resolve by subdomain or header
    const tenantSlug = request.header('x-tenant') 
      || request.subdomains()[0] // e.g., acme.my-ai-studio.com → "acme"

    if (!tenantSlug) {
      throw new Error('Tenant not specified')
    }

    // 🔎 Look up tenant in master DB
    const tenant = await Tenant.query({ connection: 'master' })
      .where('slug', tenantSlug)
      .firstOrFail()

    // ⚡️ Dynamically register tenant DB connection (overwrite per request)
    Database.manager.add('tenant', {
      client: 'pg',
      connection: {
        host: tenant.dbHost,
        port: tenant.dbPort,
        user: tenant.dbUser,
        password: tenant.dbPassword,
        database: tenant.dbName,
      },
    })

    // 🟢 Continue request
    await next()
  }
}
